"""
收益率计算模块
负责根据配置计算各种策略和合约组合的收益率序列
"""

import pandas as pd
import numpy as np
import logging
import os
from typing import Dict, List, Any, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

class ReturnsCalculator:
    """收益率计算器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化收益率计算器
        
        Args:
            config: 自定义计算配置字典
        """
        self.config = config.get('returns_calculation', {})
        self.custom_mappings = self.config.get('custom_mappings', [])
        self.symbol_groups = self.config.get('symbol_groups', {})
        self.calculation_settings = self.config.get('calculation_settings', {})
        
    def calculate_returns(self, cta_data: pd.DataFrame, position_data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        计算所有配置的收益率序列
        
        Args:
            cta_data: CTA策略数据
            position_data: 持仓资金数据
            
        Returns:
            各种收益率序列数据字典
        """
        returns_results = {}
        
        try:
            # 确保数据按日期排序
            cta_data = cta_data.sort_values('trade_date')
            position_data = position_data.sort_values('trade_date')
            
            # 按日期汇总盈亏数据
            daily_pnl = cta_data.groupby('trade_date')['profit_loss_amount'].sum().reset_index()
            
            # 为每个自定义映射计算收益率
            for mapping in self.custom_mappings:
                target = mapping['profit_loss_target']
                investment_scale = mapping['investment_scale']
                description = mapping.get('description', target)
                
                # 计算目标盈亏序列
                target_pnl = self._calculate_target_pnl(cta_data, target)
                
                # 计算收益率序列
                returns_series = self._calculate_returns_series(
                    target_pnl, position_data, investment_scale
                )
                
                if not returns_series.empty:
                    returns_results[target] = {
                        'returns_data': returns_series,
                        'description': description,
                        'investment_scale': investment_scale
                    }
                    
                    # 保存收益率数据到文件
                    self._save_returns_data(returns_series, target, description)
            
            logger.info(f"成功计算 {len(returns_results)} 个收益率序列")
            return returns_results
            
        except Exception as e:
            logger.error(f"收益率计算失败: {str(e)}")
            raise
    
    def _calculate_target_pnl(self, cta_data: pd.DataFrame, target: str) -> pd.DataFrame:
        """
        根据目标配置计算盈亏序列
        
        Args:
            cta_data: CTA策略数据
            target: 目标配置字符串
            
        Returns:
            目标盈亏序列DataFrame
        """
        if ':' not in target:
            logger.warning(f"目标配置格式错误: {target}")
            return pd.DataFrame()
        
        target_type, target_value = target.split(':', 1)
        
        if target_type == 'strategy_category':
            # 按策略类别过滤
            filtered_data = cta_data[cta_data['strategy_category'] == target_value]
            
        elif target_type == 'strategy_signal':
            # 按策略信号过滤
            filtered_data = cta_data[cta_data['strategy_signal'] == target_value]
            
        elif target_type == 'custom_symbols':
            # 按自定义合约组过滤
            if target_value in self.symbol_groups:
                symbols = self.symbol_groups[target_value]
                filtered_data = cta_data[cta_data['symbol'].isin(symbols)]
            else:
                logger.warning(f"未找到自定义合约组: {target_value}")
                return pd.DataFrame()
                
        else:
            logger.warning(f"不支持的目标类型: {target_type}")
            return pd.DataFrame()
        
        if filtered_data.empty:
            logger.warning(f"目标 {target} 没有匹配的数据")
            return pd.DataFrame()
        
        # 按日期汇总盈亏
        target_pnl = filtered_data.groupby('trade_date')['profit_loss_amount'].sum().reset_index()
        target_pnl.columns = ['trade_date', 'pnl']
        
        return target_pnl
    
    def _calculate_returns_series(self, target_pnl: pd.DataFrame, position_data: pd.DataFrame, 
                                investment_scale: str) -> pd.DataFrame:
        """
        计算收益率序列
        
        Args:
            target_pnl: 目标盈亏序列
            position_data: 持仓资金数据
            investment_scale: 投资规模字段名
            
        Returns:
            收益率序列DataFrame
        """
        if investment_scale not in position_data.columns:
            logger.warning(f"持仓数据中缺少字段: {investment_scale}")
            return pd.DataFrame()
        
        # 合并盈亏数据和持仓数据
        merged_data = pd.merge(target_pnl, position_data[['trade_date', investment_scale]], 
                              on='trade_date', how='inner')
        
        if merged_data.empty:
            logger.warning("合并后的数据为空")
            return pd.DataFrame()
        
        # 计算收益率
        merged_data['investment_amount'] = merged_data[investment_scale]
        merged_data['daily_return'] = np.where(
            merged_data['investment_amount'] != 0,
            merged_data['pnl'] / merged_data['investment_amount'],
            0
        )
        
        # 计算累计收益率
        merged_data['cumulative_return'] = (1 + merged_data['daily_return']).cumprod() - 1
        
        # 整理输出格式
        returns_series = merged_data[['trade_date', 'pnl', 'investment_amount', 
                                    'daily_return', 'cumulative_return']].copy()
        returns_series.columns = ['交易日期', '当日盈亏', '投资规模', '日收益率', '累计收益率']
        
        return returns_series
    
    def _save_returns_data(self, returns_data: pd.DataFrame, target: str, description: str):
        """
        保存收益率数据到文件
        
        Args:
            returns_data: 收益率数据
            target: 目标标识
            description: 描述信息
        """
        try:
            # 创建输出目录
            output_dir = self.calculation_settings.get('output_directory', 'reports/returns_data')
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            if not returns_data.empty:
                start_date = returns_data['交易日期'].min().strftime('%Y%m%d')
                end_date = returns_data['交易日期'].max().strftime('%Y%m%d')
                
                # 清理目标名称用于文件名
                clean_target = target.replace(':', '_').replace('/', '_')
                filename = f"returns_{clean_target}_{start_date}_{end_date}.csv"
                
                file_path = os.path.join(output_dir, filename)
                
                # 保存数据
                if self.calculation_settings.get('replace_mode', True):
                    returns_data.to_csv(file_path, index=False, encoding='utf-8-sig')
                    logger.info(f"收益率数据已保存: {file_path}")
                else:
                    if not os.path.exists(file_path):
                        returns_data.to_csv(file_path, index=False, encoding='utf-8-sig')
                        logger.info(f"收益率数据已保存: {file_path}")
                    else:
                        logger.info(f"文件已存在，跳过保存: {file_path}")
                        
        except Exception as e:
            logger.error(f"保存收益率数据失败: {str(e)}")
    
    def calculate_performance_metrics(self, returns_data: pd.DataFrame) -> Dict[str, float]:
        """
        计算基本的性能指标
        
        Args:
            returns_data: 收益率数据
            
        Returns:
            性能指标字典
        """
        if returns_data.empty or '日收益率' not in returns_data.columns:
            return {}
        
        daily_returns = returns_data['日收益率'].dropna()
        
        if len(daily_returns) == 0:
            return {}
        
        metrics = {}
        
        # 基本收益指标
        metrics['总收益率'] = returns_data['累计收益率'].iloc[-1] if not returns_data.empty else 0
        metrics['年化收益率'] = daily_returns.mean() * 252
        metrics['收益波动率'] = daily_returns.std() * np.sqrt(252)
        
        # 风险调整收益指标
        if metrics['收益波动率'] != 0:
            metrics['夏普比率'] = metrics['年化收益率'] / metrics['收益波动率']
        else:
            metrics['夏普比率'] = 0
        
        # 最大回撤
        cumulative_returns = (1 + daily_returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        metrics['最大回撤'] = drawdown.min()
        
        # 胜率
        positive_days = (daily_returns > 0).sum()
        total_days = len(daily_returns)
        metrics['胜率'] = positive_days / total_days if total_days > 0 else 0
        
        # 盈亏比
        positive_returns = daily_returns[daily_returns > 0]
        negative_returns = daily_returns[daily_returns < 0]
        
        if len(positive_returns) > 0 and len(negative_returns) > 0:
            avg_positive = positive_returns.mean()
            avg_negative = abs(negative_returns.mean())
            metrics['盈亏比'] = avg_positive / avg_negative if avg_negative != 0 else 0
        else:
            metrics['盈亏比'] = 0
        
        return metrics
    
    def generate_returns_summary(self, returns_results: Dict[str, Dict]) -> pd.DataFrame:
        """
        生成收益率汇总表
        
        Args:
            returns_results: 收益率计算结果
            
        Returns:
            收益率汇总DataFrame
        """
        summary_data = []
        
        for target, result in returns_results.items():
            returns_data = result['returns_data']
            description = result['description']
            investment_scale = result['investment_scale']
            
            # 计算性能指标
            metrics = self.calculate_performance_metrics(returns_data)
            
            summary_row = {
                '策略目标': target,
                '策略描述': description,
                '投资规模字段': investment_scale,
                '数据天数': len(returns_data),
                '总收益率': metrics.get('总收益率', 0),
                '年化收益率': metrics.get('年化收益率', 0),
                '收益波动率': metrics.get('收益波动率', 0),
                '夏普比率': metrics.get('夏普比率', 0),
                '最大回撤': metrics.get('最大回撤', 0),
                '胜率': metrics.get('胜率', 0),
                '盈亏比': metrics.get('盈亏比', 0)
            }
            
            summary_data.append(summary_row)
        
        summary_df = pd.DataFrame(summary_data)
        
        # 格式化数值列
        percentage_columns = ['总收益率', '年化收益率', '收益波动率', '最大回撤', '胜率']
        for col in percentage_columns:
            if col in summary_df.columns:
                summary_df[col] = summary_df[col].apply(lambda x: f"{x:.2%}")
        
        ratio_columns = ['夏普比率', '盈亏比']
        for col in ratio_columns:
            if col in summary_df.columns:
                summary_df[col] = summary_df[col].apply(lambda x: f"{x:.2f}")
        
        return summary_df
