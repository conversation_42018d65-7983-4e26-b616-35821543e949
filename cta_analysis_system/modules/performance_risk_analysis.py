"""
性能风险分析模块
使用quantstats库进行专业的性能和风险分析
"""

import pandas as pd
import numpy as np
import logging
import os
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Tuple
from datetime import datetime
import warnings

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'PingFang SC', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class PerformanceRiskAnalyzer:
    """性能风险分析器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化性能风险分析器
        
        Args:
            config: 分析配置字典
        """
        self.config = config
        self.risk_config = config.get('risk_analysis', {})
        self.performance_config = config.get('performance_metrics', {})
        self.var_confidence_levels = self.risk_config.get('var_confidence_levels', [0.95, 0.99])
        self.es_confidence_levels = self.risk_config.get('es_confidence_levels', [0.95, 0.99])
        self.rolling_window = self.risk_config.get('rolling_window', 250)
        self.risk_free_rate = self.risk_config.get('risk_free_rate', 0.02)
        
        # 尝试导入quantstats
        try:
            import quantstats as qs
            self.qs = qs
            self.qs_available = True
            logger.info("quantstats库加载成功")
        except ImportError:
            self.qs = None
            self.qs_available = False
            logger.warning("quantstats库未安装，将使用基础分析功能")
    
    def analyze_performance_risk(self, returns_results: Dict[str, Dict]) -> Dict[str, Any]:
        """
        执行完整的性能风险分析
        
        Args:
            returns_results: 收益率计算结果
            
        Returns:
            分析结果字典
        """
        analysis_results = {}
        
        try:
            for target, result in returns_results.items():
                returns_data = result['returns_data']
                description = result['description']
                
                if returns_data.empty or '日收益率' not in returns_data.columns:
                    logger.warning(f"跳过 {target}：收益率数据为空")
                    continue
                
                # 提取收益率序列
                returns_series = returns_data.set_index('交易日期')['日收益率']
                
                # 执行分析
                target_analysis = {}
                
                # 1. 性能分析
                target_analysis['performance_metrics'] = self._performance_analysis(returns_series)
                
                # 2. 风险分析
                target_analysis['risk_metrics'] = self._risk_analysis(returns_series)
                
                # 3. 回撤分析
                target_analysis['drawdown_analysis'] = self._drawdown_analysis(returns_series)
                
                # 4. 生成图表
                target_analysis['charts'] = self._generate_charts(returns_series, target, description)
                
                # 5. quantstats分析（如果可用）
                if self.qs_available:
                    target_analysis['quantstats_analysis'] = self._quantstats_analysis(returns_series, target)
                
                analysis_results[target] = target_analysis
                
                # 保存中间数据
                self._save_intermediate_data(target_analysis, target)
            
            logger.info(f"性能风险分析完成，共分析 {len(analysis_results)} 个策略")
            return analysis_results
            
        except Exception as e:
            logger.error(f"性能风险分析失败: {str(e)}")
            raise
    
    def _performance_analysis(self, returns_series: pd.Series) -> Dict[str, float]:
        """
        性能分析
        
        Args:
            returns_series: 收益率序列
            
        Returns:
            性能指标字典
        """
        metrics = {}
        
        # 基本收益指标
        total_return = (1 + returns_series).prod() - 1
        annual_return = (1 + returns_series.mean()) ** 252 - 1
        volatility = returns_series.std() * np.sqrt(252)
        
        metrics['总收益率'] = total_return
        metrics['年化收益率'] = annual_return
        metrics['年化波动率'] = volatility
        
        # 风险调整收益
        if volatility != 0:
            sharpe_ratio = (annual_return - self.risk_free_rate) / volatility
            metrics['夏普比率'] = sharpe_ratio
        else:
            metrics['夏普比率'] = 0
        
        # 下行风险指标
        negative_returns = returns_series[returns_series < 0]
        if len(negative_returns) > 0:
            downside_deviation = negative_returns.std() * np.sqrt(252)
            if downside_deviation != 0:
                sortino_ratio = (annual_return - self.risk_free_rate) / downside_deviation
                metrics['索提诺比率'] = sortino_ratio
            else:
                metrics['索提诺比率'] = 0
            metrics['下行波动率'] = downside_deviation
        else:
            metrics['索提诺比率'] = 0
            metrics['下行波动率'] = 0
        
        # 胜率和盈亏比
        positive_days = (returns_series > 0).sum()
        total_days = len(returns_series)
        metrics['胜率'] = positive_days / total_days if total_days > 0 else 0
        
        positive_returns = returns_series[returns_series > 0]
        negative_returns = returns_series[returns_series < 0]
        
        if len(positive_returns) > 0 and len(negative_returns) > 0:
            avg_positive = positive_returns.mean()
            avg_negative = abs(negative_returns.mean())
            metrics['盈亏比'] = avg_positive / avg_negative if avg_negative != 0 else 0
        else:
            metrics['盈亏比'] = 0
        
        return metrics
    
    def _risk_analysis(self, returns_series: pd.Series) -> Dict[str, Any]:
        """
        风险分析
        
        Args:
            returns_series: 收益率序列
            
        Returns:
            风险指标字典
        """
        risk_metrics = {}
        
        # VaR计算
        var_results = {}
        for confidence_level in self.var_confidence_levels:
            var_value = returns_series.quantile(1 - confidence_level)
            var_results[f'VaR_{int(confidence_level*100)}%'] = var_value
        
        risk_metrics['VaR'] = var_results
        
        # ES (Expected Shortfall) 计算
        es_results = {}
        for confidence_level in self.es_confidence_levels:
            var_threshold = returns_series.quantile(1 - confidence_level)
            tail_losses = returns_series[returns_series <= var_threshold]
            if len(tail_losses) > 0:
                es_value = tail_losses.mean()
            else:
                es_value = var_threshold
            es_results[f'ES_{int(confidence_level*100)}%'] = es_value
        
        risk_metrics['ES'] = es_results
        
        # 滚动风险指标
        if len(returns_series) >= self.rolling_window:
            rolling_vol = returns_series.rolling(window=self.rolling_window).std() * np.sqrt(252)
            risk_metrics['滚动波动率统计'] = {
                '平均': rolling_vol.mean(),
                '最大': rolling_vol.max(),
                '最小': rolling_vol.min(),
                '标准差': rolling_vol.std()
            }
        
        # 尾部风险
        skewness = returns_series.skew()
        kurtosis = returns_series.kurtosis()
        
        risk_metrics['偏度'] = skewness
        risk_metrics['峰度'] = kurtosis
        
        return risk_metrics
    
    def _drawdown_analysis(self, returns_series: pd.Series) -> Dict[str, Any]:
        """
        回撤分析
        
        Args:
            returns_series: 收益率序列
            
        Returns:
            回撤分析结果
        """
        # 计算累计收益
        cumulative_returns = (1 + returns_series).cumprod()
        
        # 计算回撤
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        
        drawdown_analysis = {}
        
        # 基本回撤指标
        drawdown_analysis['最大回撤'] = drawdown.min()
        drawdown_analysis['平均回撤'] = drawdown[drawdown < 0].mean() if (drawdown < 0).any() else 0
        
        # 回撤期间分析
        drawdown_periods = self._identify_drawdown_periods(drawdown)
        
        if drawdown_periods:
            drawdown_analysis['回撤期间数量'] = len(drawdown_periods)
            drawdown_analysis['平均回撤持续天数'] = np.mean([period['duration'] for period in drawdown_periods])
            drawdown_analysis['最长回撤持续天数'] = max([period['duration'] for period in drawdown_periods])
            
            # 最大回撤期间详情
            max_drawdown_period = max(drawdown_periods, key=lambda x: abs(x['max_drawdown']))
            drawdown_analysis['最大回撤期间'] = {
                '开始日期': max_drawdown_period['start_date'],
                '结束日期': max_drawdown_period['end_date'],
                '持续天数': max_drawdown_period['duration'],
                '最大回撤值': max_drawdown_period['max_drawdown']
            }
        else:
            drawdown_analysis['回撤期间数量'] = 0
            drawdown_analysis['平均回撤持续天数'] = 0
            drawdown_analysis['最长回撤持续天数'] = 0
        
        # Calmar比率
        if drawdown_analysis['最大回撤'] != 0:
            annual_return = (1 + returns_series.mean()) ** 252 - 1
            calmar_ratio = annual_return / abs(drawdown_analysis['最大回撤'])
            drawdown_analysis['Calmar比率'] = calmar_ratio
        else:
            drawdown_analysis['Calmar比率'] = 0
        
        return drawdown_analysis
    
    def _identify_drawdown_periods(self, drawdown: pd.Series) -> List[Dict]:
        """
        识别回撤期间
        
        Args:
            drawdown: 回撤序列
            
        Returns:
            回撤期间列表
        """
        periods = []
        in_drawdown = False
        start_date = None
        
        for date, dd_value in drawdown.items():
            if dd_value < 0 and not in_drawdown:
                # 开始回撤
                in_drawdown = True
                start_date = date
                min_drawdown = dd_value
            elif dd_value < 0 and in_drawdown:
                # 继续回撤
                min_drawdown = min(min_drawdown, dd_value)
            elif dd_value >= 0 and in_drawdown:
                # 回撤结束
                in_drawdown = False
                periods.append({
                    'start_date': start_date,
                    'end_date': date,
                    'duration': (date - start_date).days,
                    'max_drawdown': min_drawdown
                })
        
        # 处理未结束的回撤
        if in_drawdown:
            periods.append({
                'start_date': start_date,
                'end_date': drawdown.index[-1],
                'duration': (drawdown.index[-1] - start_date).days,
                'max_drawdown': min_drawdown
            })
        
        return periods

    def _generate_charts(self, returns_series: pd.Series, target: str, description: str) -> Dict[str, str]:
        """
        生成分析图表

        Args:
            returns_series: 收益率序列
            target: 目标标识
            description: 描述信息

        Returns:
            图表文件路径字典
        """
        chart_paths = {}

        try:
            # 创建图表输出目录
            charts_dir = "plots"
            os.makedirs(charts_dir, exist_ok=True)

            # 清理目标名称用于文件名
            clean_target = target.replace(':', '_').replace('/', '_')

            # 1. 累计收益曲线
            fig, ax = plt.subplots(figsize=(12, 6))
            cumulative_returns = (1 + returns_series).cumprod()
            ax.plot(cumulative_returns.index, cumulative_returns.values, linewidth=2, color='#0066CC')
            ax.set_title(f'{description} - 累计收益曲线', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期')
            ax.set_ylabel('累计收益率')
            ax.grid(True, alpha=0.3)
            plt.xticks(rotation=45)
            plt.tight_layout()

            chart_path = os.path.join(charts_dir, f'{clean_target}_cumulative_returns.png')
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            chart_paths['累计收益曲线'] = chart_path

            # 2. 回撤图
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

            # 上图：累计收益
            ax1.plot(cumulative_returns.index, cumulative_returns.values, linewidth=2, color='#0066CC')
            ax1.set_title(f'{description} - 累计收益与回撤分析', fontsize=14, fontweight='bold')
            ax1.set_ylabel('累计收益率')
            ax1.grid(True, alpha=0.3)

            # 下图：回撤
            running_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - running_max) / running_max
            ax2.fill_between(drawdown.index, drawdown.values, 0, color='#FF0000', alpha=0.3)
            ax2.plot(drawdown.index, drawdown.values, color='#FF0000', linewidth=1)
            ax2.set_ylabel('回撤')
            ax2.set_xlabel('日期')
            ax2.grid(True, alpha=0.3)

            plt.xticks(rotation=45)
            plt.tight_layout()

            chart_path = os.path.join(charts_dir, f'{clean_target}_drawdown_analysis.png')
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            chart_paths['回撤分析'] = chart_path

            # 3. 收益率分布直方图
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.hist(returns_series.values, bins=50, alpha=0.7, color='#0066CC', edgecolor='black')
            ax.axvline(returns_series.mean(), color='#FF0000', linestyle='--', linewidth=2, label=f'均值: {returns_series.mean():.4f}')
            ax.axvline(returns_series.quantile(0.05), color='#FF0000', linestyle=':', linewidth=2, label=f'5% VaR: {returns_series.quantile(0.05):.4f}')
            ax.set_title(f'{description} - 收益率分布', fontsize=14, fontweight='bold')
            ax.set_xlabel('日收益率')
            ax.set_ylabel('频数')
            ax.legend()
            ax.grid(True, alpha=0.3)
            plt.tight_layout()

            chart_path = os.path.join(charts_dir, f'{clean_target}_returns_distribution.png')
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            chart_paths['收益率分布'] = chart_path

            # 4. 滚动指标图
            if len(returns_series) >= 60:  # 至少60天数据才绘制滚动指标
                fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

                # 滚动夏普比率
                rolling_sharpe = returns_series.rolling(window=60).mean() / returns_series.rolling(window=60).std() * np.sqrt(252)
                ax1.plot(rolling_sharpe.index, rolling_sharpe.values, linewidth=2, color='#00AA00')
                ax1.set_title(f'{description} - 滚动指标分析', fontsize=14, fontweight='bold')
                ax1.set_ylabel('滚动夏普比率 (60天)')
                ax1.grid(True, alpha=0.3)

                # 滚动波动率
                rolling_vol = returns_series.rolling(window=60).std() * np.sqrt(252)
                ax2.plot(rolling_vol.index, rolling_vol.values, linewidth=2, color='#FF6600')
                ax2.set_ylabel('滚动波动率 (60天)')
                ax2.set_xlabel('日期')
                ax2.grid(True, alpha=0.3)

                plt.xticks(rotation=45)
                plt.tight_layout()

                chart_path = os.path.join(charts_dir, f'{clean_target}_rolling_metrics.png')
                plt.savefig(chart_path, dpi=300, bbox_inches='tight')
                plt.close()
                chart_paths['滚动指标'] = chart_path

            logger.info(f"为 {target} 生成了 {len(chart_paths)} 个图表")

        except Exception as e:
            logger.error(f"生成图表失败: {str(e)}")

        return chart_paths

    def _quantstats_analysis(self, returns_series: pd.Series, target: str) -> Dict[str, Any]:
        """
        使用quantstats进行专业分析

        Args:
            returns_series: 收益率序列
            target: 目标标识

        Returns:
            quantstats分析结果
        """
        if not self.qs_available:
            return {}

        try:
            qs_results = {}

            # 基本统计
            qs_results['基本统计'] = {
                'CAGR': self.qs.stats.cagr(returns_series),
                'Sharpe': self.qs.stats.sharpe(returns_series),
                'Sortino': self.qs.stats.sortino(returns_series),
                'Max Drawdown': self.qs.stats.max_drawdown(returns_series),
                'Volatility': self.qs.stats.volatility(returns_series),
                'Calmar': self.qs.stats.calmar(returns_series)
            }

            # 风险指标
            qs_results['风险指标'] = {
                'VaR': self.qs.stats.var(returns_series),
                'CVaR': self.qs.stats.cvar(returns_series),
                'Skew': self.qs.stats.skew(returns_series),
                'Kurtosis': self.qs.stats.kurtosis(returns_series)
            }

            # 生成quantstats报告
            clean_target = target.replace(':', '_').replace('/', '_')
            report_path = f"reports/{clean_target}_quantstats_report.html"

            # 确保目录存在
            os.makedirs(os.path.dirname(report_path), exist_ok=True)

            # 生成HTML报告
            self.qs.reports.html(returns_series, output=report_path, title=f"{target} 策略分析报告")
            qs_results['报告路径'] = report_path

            logger.info(f"quantstats分析完成: {target}")

        except Exception as e:
            logger.error(f"quantstats分析失败: {str(e)}")
            qs_results = {'错误': str(e)}

        return qs_results

    def _save_intermediate_data(self, analysis_result: Dict[str, Any], target: str):
        """
        保存中间分析数据

        Args:
            analysis_result: 分析结果
            target: 目标标识
        """
        try:
            # 创建中间数据目录
            risk_dir = "intermediate_data/risk_analysis"
            drawdown_dir = "intermediate_data/drawdown_analysis"
            os.makedirs(risk_dir, exist_ok=True)
            os.makedirs(drawdown_dir, exist_ok=True)

            clean_target = target.replace(':', '_').replace('/', '_')

            # 保存风险分析数据
            if 'risk_metrics' in analysis_result:
                risk_data = []
                risk_metrics = analysis_result['risk_metrics']

                # VaR数据
                if 'VaR' in risk_metrics:
                    for var_type, var_value in risk_metrics['VaR'].items():
                        risk_data.append({
                            '指标类型': 'VaR',
                            '置信水平': var_type,
                            '数值': var_value
                        })

                # ES数据
                if 'ES' in risk_metrics:
                    for es_type, es_value in risk_metrics['ES'].items():
                        risk_data.append({
                            '指标类型': 'ES',
                            '置信水平': es_type,
                            '数值': es_value
                        })

                if risk_data:
                    risk_df = pd.DataFrame(risk_data)
                    risk_file = os.path.join(risk_dir, f'{clean_target}_risk_analysis.xlsx')
                    risk_df.to_excel(risk_file, index=False)

            # 保存回撤分析数据
            if 'drawdown_analysis' in analysis_result:
                drawdown_data = []
                drawdown_metrics = analysis_result['drawdown_analysis']

                for metric, value in drawdown_metrics.items():
                    if isinstance(value, dict):
                        for sub_metric, sub_value in value.items():
                            drawdown_data.append({
                                '指标类别': metric,
                                '指标名称': sub_metric,
                                '数值': sub_value
                            })
                    else:
                        drawdown_data.append({
                            '指标类别': '基本指标',
                            '指标名称': metric,
                            '数值': value
                        })

                if drawdown_data:
                    drawdown_df = pd.DataFrame(drawdown_data)
                    drawdown_file = os.path.join(drawdown_dir, f'{clean_target}_drawdown_analysis.xlsx')
                    drawdown_df.to_excel(drawdown_file, index=False)

            logger.info(f"中间数据保存完成: {target}")

        except Exception as e:
            logger.error(f"保存中间数据失败: {str(e)}")
