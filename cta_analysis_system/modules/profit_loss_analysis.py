"""
盈亏分析模块
负责多维度盈亏透视汇总和自定义分类盈亏计算
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Tuple
import json

logger = logging.getLogger(__name__)

class ProfitLossAnalyzer:
    """盈亏分析器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化盈亏分析器
        
        Args:
            config: 分析配置字典
        """
        self.config = config.get('profit_loss_analysis', {})
        self.primary_categories = self.config.get('primary_categories', [])
        self.pivot_fields = self.config.get('pivot_fields', [])
        self.custom_calculations = self.config.get('custom_calculations', {})
        
    def analyze_profit_loss(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """
        执行完整的盈亏分析
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            分析结果字典
        """
        results = {}
        
        try:
            # 1. 多维度盈亏透视汇总
            results['multi_dimension_analysis'] = self._multi_dimension_pivot(cta_data)
            
            # 2. 自定义分类盈亏分析
            results['custom_category_analysis'] = self._custom_category_analysis(cta_data)
            
            # 3. 总体盈亏统计
            results['overall_statistics'] = self._calculate_overall_statistics(cta_data)
            
            # 4. 按时间维度的盈亏分析
            results['time_dimension_analysis'] = self._time_dimension_analysis(cta_data)
            
            logger.info("盈亏分析完成")
            return results
            
        except Exception as e:
            logger.error(f"盈亏分析失败: {str(e)}")
            raise
    
    def _multi_dimension_pivot(self, cta_data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        多维度盈亏透视汇总
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            各维度透视结果
        """
        pivot_results = {}
        
        for primary_category in self.primary_categories:
            if primary_category not in cta_data.columns:
                logger.warning(f"数据中缺少字段: {primary_category}")
                continue
            
            category_results = {}
            
            # 获取该一级分类的所有唯一值
            unique_values = cta_data[primary_category].unique()
            
            for value in unique_values:
                # 过滤数据
                filtered_data = cta_data[cta_data[primary_category] == value]
                
                # 对每个透视字段进行汇总
                pivot_data = {}
                for pivot_field in self.pivot_fields:
                    if pivot_field in filtered_data.columns:
                        pivot_summary = filtered_data.groupby(pivot_field)['profit_loss_amount'].agg([
                            'sum', 'mean', 'count', 'std'
                        ]).round(2)
                        pivot_summary.columns = ['总盈亏', '平均盈亏', '交易次数', '盈亏标准差']
                        pivot_data[pivot_field] = pivot_summary
                
                category_results[f"{primary_category}_{value}"] = pivot_data
            
            pivot_results[primary_category] = category_results
        
        return pivot_results
    
    def _custom_category_analysis(self, cta_data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        自定义分类盈亏分析
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            自定义分类分析结果
        """
        custom_results = {}
        
        # 处理自定义symbol_category映射
        if 'symbol_category_mapping' in self.custom_calculations:
            symbol_mapping = self.custom_calculations['symbol_category_mapping']
            custom_symbol_data = self._apply_custom_mapping(
                cta_data, 'symbol_category', symbol_mapping
            )
            
            if not custom_symbol_data.empty:
                custom_symbol_summary = custom_symbol_data.groupby('custom_category')['profit_loss_amount'].agg([
                    'sum', 'mean', 'count', 'std'
                ]).round(2)
                custom_symbol_summary.columns = ['总盈亏', '平均盈亏', '交易次数', '盈亏标准差']
                custom_results['custom_symbol_category'] = custom_symbol_summary
        
        # 处理自定义industry映射
        if 'industry_mapping' in self.custom_calculations:
            industry_mapping = self.custom_calculations['industry_mapping']
            custom_industry_data = self._apply_custom_mapping(
                cta_data, 'industry', industry_mapping
            )
            
            if not custom_industry_data.empty:
                custom_industry_summary = custom_industry_data.groupby('custom_category')['profit_loss_amount'].agg([
                    'sum', 'mean', 'count', 'std'
                ]).round(2)
                custom_industry_summary.columns = ['总盈亏', '平均盈亏', '交易次数', '盈亏标准差']
                custom_results['custom_industry'] = custom_industry_summary
        
        return custom_results
    
    def _apply_custom_mapping(self, data: pd.DataFrame, field: str, mapping: Dict[str, List[str]]) -> pd.DataFrame:
        """
        应用自定义映射
        
        Args:
            data: 原始数据
            field: 要映射的字段
            mapping: 映射规则
            
        Returns:
            应用映射后的数据
        """
        if field not in data.columns:
            logger.warning(f"数据中缺少字段: {field}")
            return pd.DataFrame()
        
        # 创建映射字典
        value_to_category = {}
        for category, values in mapping.items():
            for value in values:
                value_to_category[value] = category
        
        # 应用映射
        mapped_data = data.copy()
        mapped_data['custom_category'] = mapped_data[field].map(value_to_category)
        
        # 过滤掉未映射的数据
        mapped_data = mapped_data.dropna(subset=['custom_category'])
        
        return mapped_data
    
    def _calculate_overall_statistics(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """
        计算总体盈亏统计
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            总体统计结果
        """
        stats = {}
        
        # 基本统计
        stats['总盈亏'] = cta_data['profit_loss_amount'].sum()
        stats['平均每日盈亏'] = cta_data.groupby('trade_date')['profit_loss_amount'].sum().mean()
        stats['总交易次数'] = len(cta_data)
        stats['盈利交易次数'] = len(cta_data[cta_data['profit_loss_amount'] > 0])
        stats['亏损交易次数'] = len(cta_data[cta_data['profit_loss_amount'] < 0])
        stats['胜率'] = stats['盈利交易次数'] / stats['总交易次数'] * 100 if stats['总交易次数'] > 0 else 0
        
        # 盈亏分布
        stats['最大单笔盈利'] = cta_data['profit_loss_amount'].max()
        stats['最大单笔亏损'] = cta_data['profit_loss_amount'].min()
        stats['盈亏标准差'] = cta_data['profit_loss_amount'].std()
        
        # 按策略类别统计
        if 'strategy_category' in cta_data.columns:
            strategy_stats = cta_data.groupby('strategy_category')['profit_loss_amount'].agg([
                'sum', 'mean', 'count'
            ]).round(2)
            strategy_stats.columns = ['总盈亏', '平均盈亏', '交易次数']
            stats['策略类别统计'] = strategy_stats
        
        return stats
    
    def _time_dimension_analysis(self, cta_data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        按时间维度分析盈亏
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            时间维度分析结果
        """
        time_results = {}
        
        # 按日期汇总
        daily_pnl = cta_data.groupby('trade_date')['profit_loss_amount'].sum().reset_index()
        daily_pnl.columns = ['交易日期', '当日盈亏']
        daily_pnl['累计盈亏'] = daily_pnl['当日盈亏'].cumsum()
        time_results['daily_summary'] = daily_pnl
        
        # 按月汇总
        cta_data_copy = cta_data.copy()
        cta_data_copy['year_month'] = cta_data_copy['trade_date'].dt.to_period('M')
        monthly_pnl = cta_data_copy.groupby('year_month')['profit_loss_amount'].agg([
            'sum', 'mean', 'count'
        ]).round(2)
        monthly_pnl.columns = ['月度盈亏', '平均每日盈亏', '交易天数']
        time_results['monthly_summary'] = monthly_pnl
        
        # 按周汇总
        cta_data_copy['year_week'] = cta_data_copy['trade_date'].dt.to_period('W')
        weekly_pnl = cta_data_copy.groupby('year_week')['profit_loss_amount'].agg([
            'sum', 'mean', 'count'
        ]).round(2)
        weekly_pnl.columns = ['周度盈亏', '平均每日盈亏', '交易天数']
        time_results['weekly_summary'] = weekly_pnl
        
        return time_results
    
    def generate_summary_report(self, analysis_results: Dict[str, Any]) -> str:
        """
        生成盈亏分析摘要报告
        
        Args:
            analysis_results: 分析结果
            
        Returns:
            摘要报告文本
        """
        report_lines = []
        report_lines.append("# 盈亏分析报告")
        report_lines.append("")
        
        # 总体统计
        if 'overall_statistics' in analysis_results:
            stats = analysis_results['overall_statistics']
            report_lines.append("## 总体统计")
            report_lines.append(f"- 总盈亏: {stats.get('总盈亏', 0):,.2f} 元")
            report_lines.append(f"- 平均每日盈亏: {stats.get('平均每日盈亏', 0):,.2f} 元")
            report_lines.append(f"- 总交易次数: {stats.get('总交易次数', 0):,} 次")
            report_lines.append(f"- 胜率: {stats.get('胜率', 0):.2f}%")
            report_lines.append("")
        
        # 多维度分析摘要
        if 'multi_dimension_analysis' in analysis_results:
            report_lines.append("## 多维度分析摘要")
            multi_dim = analysis_results['multi_dimension_analysis']
            for category, results in multi_dim.items():
                report_lines.append(f"### {category}")
                for subcategory, pivot_data in results.items():
                    report_lines.append(f"- {subcategory}: 包含 {len(pivot_data)} 个透视维度")
                report_lines.append("")
        
        return "\n".join(report_lines)
